import { Platform, Linking, Alert } from 'react-native';
import { PermissionsAndroid } from 'react-native';

// Helper function to open device settings
export const openDeviceSettings = () => {
  try {
    if (Platform.OS === 'android') {
      Linking.openSettings();
    } else {
      Linking.openURL('app-settings:');
    }
  } catch (error) {
    console.error('Error opening device settings:', error);
    Alert.alert('Error', 'Unable to open device settings. Please open settings manually.');
  }
};

// Check if notifications are enabled (Android only)
export const checkNotificationPermission = async (): Promise<boolean> => {
  if (Platform.OS === 'android' && Platform.Version >= 33) {
    try {
      const granted = await PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
      );
      return granted;
    } catch (error) {
      console.error('Error checking notification permission:', error);
      return false;
    }
  }
  // For iOS and Android < 13, assume permissions are granted
  return true;
};

// Show permission denied alert with settings option
export const showPermissionDeniedAlert = (
  title: string = 'Permission Required',
  message: string = 'Please enable notifications in your device settings to receive important updates.'
) => {
  Alert.alert(
    title,
    message,
    [
      { text: 'Cancel', style: 'cancel' },
      { 
        text: 'Open Settings', 
        onPress: () => openDeviceSettings()
      }
    ]
  );
};

// Show notification settings alert
export const showNotificationSettingsAlert = () => {
  Alert.alert(
    'Notification Settings',
    'To receive push notifications, please enable them in your device settings.',
    [
      { text: 'Cancel', style: 'cancel' },
      { 
        text: 'Open Settings', 
        onPress: () => openDeviceSettings()
      }
    ]
  );
};
