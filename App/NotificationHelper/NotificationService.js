// src/services/NotificationService.js
import PushNotification from 'react-native-push-notification';
import { Platform, PermissionsAndroid, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { apiUrl } from '../config/apiUrl';

let isNotificationSetupComplete = false;

// Register FCM token with server
const registerFCMTokenWithServer = async (token) => {
  try {
    console.log('Registering FCM token with server:', token);

    // Get user token (authentication token)
    const userToken = await AsyncStorage.getItem('token');
    if (!userToken) {
      console.log('User not logged in, will register token later');
      return false;
    }

    // Send FCM token to server
    const response = await axios.post(
      `${apiUrl}/notifications/register-device`,
      {
        deviceToken: token.token,
        deviceType: Platform.OS.toUpperCase(),
        deviceModel: Platform.OS === 'ios' ? 'iOS Device' : 'Android Device'
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${userToken}`
        }
      }
    );

    if (response.data && response.data.success) {
      console.log('FCM token registered successfully');
      await AsyncStorage.setItem('fcmTokenRegistered', 'true');
      return true;
    } else {
      console.error('Failed to register FCM token:', response.data);
      return false;
    }
  } catch (error) {
    console.error('Error registering FCM token:', error);
    return false;
  }
};

const configureNotifications = () => {
  PushNotification.configure({
    onRegister: async function (token) {
      console.log('FCM TOKEN RECEIVED:', token);

      // Store token locally
      await AsyncStorage.setItem('fcmToken', token.token);

      // Register with server
      const registered = await registerFCMTokenWithServer(token);
      if (!registered) {
        console.log('Will try to register token again on next app launch');
      }
    },
    onNotification: function (notification) {
      console.log('NOTIFICATION RECEIVED:', notification);

      // Handle notification when app is in foreground
      if (notification.foreground) {
        // You can show an in-app alert or update UI
        console.log('App is in foreground, handling notification');
      }

      // Handle notification when user taps on it
      if (notification.userInteraction) {
        console.log('User tapped on notification');
        // You can navigate to specific screen based on notification data
      }

      // Required on iOS only
      notification.finish(PushNotification.ForegroundPresentationOptions.Badge);
    },
    permissions: {
      alert: true,
      badge: true,
      sound: true,
    },
    popInitialNotification: true,
    requestPermissions: false, // We'll handle permissions manually
  });
};

const requestNotificationPermission = async () => {
  try {
    console.log("For Permission ask::;;;");
    console.log("Platform:", Platform.OS, "API Level:", Platform.Version);

    if (Platform.OS === 'android' && Platform.Version >= 33) {
      console.log("Requesting POST_NOTIFICATIONS permission...");
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
        {
          title: 'Notification Permission',
          message: 'Allow this app to send notifications?',
          buttonNeutral: 'Ask Me Later',
          buttonNegative: 'Cancel',
          buttonPositive: 'OK',
        }
      );
      console.log("Permission result:", granted);
      if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
        console.log("Permission denied by user");
        Alert.alert(
          'Permission Denied',
          'Notifications are disabled. You can enable them in the app settings.'
        );
        return false;
      }
      console.log("Permission granted");
      return true;
    } else if (Platform.OS === 'ios') {
      console.log("Requesting iOS notification permissions...");
      const result = await PushNotification.requestPermissions();
      console.log("iOS Permission result:", result);
      if (!result.alert) {
        console.log("iOS notification permission denied");
        Alert.alert(
          'Permission Denied',
          'Notifications are disabled. You can enable them in the app settings.'
        );
        return false;
      }
      console.log("iOS permission granted");
      return true;
    }
    console.log("No permission request needed (Android < 13)");
    return true; // For Android < 13, no runtime permission needed
  } catch (error) {
    console.error('Error requesting notification permission:', error);
    return false;
  }
};

const createNotificationChannel = async () => {
  if (Platform.OS === 'android') {
    return new Promise((resolve) => {
      PushNotification.createChannel(
        {
          channelId: 'classwork-channel-v3', // Changed again to avoid conflicts
          channelName: 'Classwork Notifications',
          channelDescription: 'Notifications for classwork updates',
          soundName: 'default',
          importance: 4,
          vibrate: true,
        },
        (created) => {
          console.log(`Channel created: ${created}`);
          resolve(created);
        }
      );
    });
  }
  return true; // No channel needed for iOS
};

const scheduleLocalNotification = (title, message, date = new Date(Date.now() + 5 * 1000)) => {
  if (!isNotificationSetupComplete) {
    console.log('Cannot schedule notification: Setup not complete');
    return;
  }
  try {
    PushNotification.localNotificationSchedule({
      channelId: 'classwork-channel-v3', // Match the new channelId
      title: title,
      message: message,
      date: date,
      allowWhileIdle: true,
      playSound: true,
      soundName: 'default',
      vibrate: true,
      vibration: 300,
    });
    console.log('Notification scheduled:', { title, message, date });
  } catch (error) {
    console.error('Error scheduling notification:', error);
  }
};

const setupNotifications = async () => {
  try {
    const permissionGranted = await requestNotificationPermission();
    if (permissionGranted) {
      configureNotifications();
      const channelCreated = await createNotificationChannel();
      if (channelCreated) {
        isNotificationSetupComplete = true;
        console.log('Notifications configured successfully');
      } else {
        console.error('Failed to create notification channel');
        Alert.alert(
          'Notification Setup Failed',
          'Unable to create notification channel. Notifications may not work.'
        );
      }
    } else {
      console.log('Notification permission denied');
      Alert.alert(
        'Notifications Disabled',
        'You have denied notification permissions. You can enable them in the app settings.'
      );
    }
  } catch (error) {
    console.error('Error setting up notifications:', error);
    Alert.alert('Error', 'Failed to initialize notifications. Please try again.');
  }
};

export { setupNotifications, scheduleLocalNotification, isNotificationSetupComplete };