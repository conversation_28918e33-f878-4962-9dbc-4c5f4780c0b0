// src/services/NotificationService.js
import PushNotification from 'react-native-push-notification';
import { Platform, PermissionsAndroid, Alert, Linking } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { apiUrl } from '../config/apiUrl';

// Helper function to open device settings
const openDeviceSettings = () => {
  if (Platform.OS === 'android') {
    Linking.openSettings();
  } else {
    Linking.openURL('app-settings:');
  }
};

let isNotificationSetupComplete = false;

// Register FCM token with server
const registerFCMTokenWithServer = async (token) => {
  try {
    console.log('Registering FCM token with server:', token);

    // Get user token (authentication token)
    const userToken = await AsyncStorage.getItem('token');
    if (!userToken) {
      console.log('User not logged in, will register token later');
      return false;
    }

    // Send FCM token to server
    const response = await axios.post(
      `${apiUrl}/notifications/register-device`,
      {
        deviceToken: token.token,
        deviceType: Platform.OS.toUpperCase(),
        deviceModel: Platform.OS === 'ios' ? 'iOS Device' : 'Android Device'
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${userToken}`
        }
      }
    );

    if (response.data && response.data.success) {
      console.log('FCM token registered successfully');
      await AsyncStorage.setItem('fcmTokenRegistered', 'true');
      return true;
    } else {
      console.error('Failed to register FCM token:', response.data);
      return false;
    }
  } catch (error) {
    console.error('Error registering FCM token:', error);
    return false;
  }
};

const configureNotifications = () => {
  PushNotification.configure({
    onRegister: async function (token) {
      console.log('FCM TOKEN RECEIVED:', token);

      // Store token locally
      await AsyncStorage.setItem('fcmToken', token.token);

      // Register with server
      const registered = await registerFCMTokenWithServer(token);
      if (!registered) {
        console.log('Will try to register token again on next app launch');
      }
    },
    onNotification: function (notification) {
      console.log('NOTIFICATION RECEIVED:', notification);

      // Handle notification when app is in foreground
      if (notification.foreground) {
        // You can show an in-app alert or update UI
        console.log('App is in foreground, handling notification');
      }

      // Handle notification when user taps on it
      if (notification.userInteraction) {
        console.log('User tapped on notification');
        // You can navigate to specific screen based on notification data
      }

      // Required on iOS only
      notification.finish(PushNotification.ForegroundPresentationOptions.Badge);
    },
    permissions: {
      alert: true,
      badge: true,
      sound: true,
    },
    popInitialNotification: true,
    requestPermissions: false, // We'll handle permissions manually
  });
};

const requestNotificationPermission = async () => {
  try {
    console.log("Requesting notification permissions...");
    console.log("Platform:", Platform.OS, "API Level:", Platform.Version);

    if (Platform.OS === 'android') {
      // For Android 13+ (API 33+), we need to request POST_NOTIFICATIONS permission
      if (Platform.Version >= 33) {
        console.log("Android 13+: Requesting POST_NOTIFICATIONS permission...");

        // Check if we already have the permission
        const hasPermission = await PermissionsAndroid.check(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS
        );

        if (hasPermission) {
          console.log("POST_NOTIFICATIONS permission already granted");
          return true;
        }

        // Request the permission
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
          {
            title: 'Notification Permission',
            message: 'UEST needs permission to send you notifications about your classes, quizzes, and important updates.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'Allow',
          }
        );

        console.log("Permission result:", granted);

        if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
          console.log("Permission denied by user");

          // Show alert with option to open settings
          Alert.alert(
            'Notification Permission Required',
            'To receive important updates about your classes and quizzes, please enable notifications in your device settings.',
            [
              { text: 'Cancel', style: 'cancel' },
              {
                text: 'Open Settings',
                onPress: () => {
                  // Open app settings
                  PushNotification.openSettings();
                }
              }
            ]
          );
          return false;
        }

        console.log("Permission granted");
        return true;
      }
      // For Android 12 and below, permissions are granted at install time
      else {
        console.log("Android 12 or below: Permissions granted at install time");
        return true;
      }
    }
    // For iOS, use the PushNotification API
    else if (Platform.OS === 'ios') {
      console.log("iOS: Requesting notification permissions...");

      const result = await PushNotification.requestPermissions({
        alert: true,
        badge: true,
        sound: true,
      });

      console.log("iOS Permission result:", result);

      if (!result.alert) {
        console.log("iOS notification permission denied");

        Alert.alert(
          'Notification Permission Required',
          'To receive important updates about your classes and quizzes, please enable notifications in your device settings.',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Open Settings',
              onPress: () => {
                // Open app settings
                PushNotification.openSettings();
              }
            }
          ]
        );
        return false;
      }

      console.log("iOS permission granted");
      return true;
    }

    // Default fallback
    console.log("Unknown platform, assuming permissions granted");
    return true;
  } catch (error) {
    console.error('Error requesting notification permission:', error);
    return false;
  }
};

const createNotificationChannel = async () => {
  if (Platform.OS === 'android') {
    return new Promise((resolve) => {
      // Create multiple notification channels for different types
      const channels = [
        {
          channelId: 'classwork-channel-v3',
          channelName: 'Classwork Notifications',
          channelDescription: 'Notifications for classwork updates and assignments',
          soundName: 'default',
          importance: 4, // HIGH
          vibrate: true,
        },
        {
          channelId: 'quiz-channel',
          channelName: 'Quiz Notifications',
          channelDescription: 'Notifications for daily quizzes and results',
          soundName: 'default',
          importance: 4, // HIGH
          vibrate: true,
        },
        {
          channelId: 'general-channel',
          channelName: 'General Notifications',
          channelDescription: 'General app notifications and updates',
          soundName: 'default',
          importance: 3, // DEFAULT
          vibrate: true,
        }
      ];

      let channelsCreated = 0;
      const totalChannels = channels.length;

      channels.forEach((channel) => {
        PushNotification.createChannel(
          channel,
          (created) => {
            console.log(`Channel ${channel.channelId} created: ${created}`);
            channelsCreated++;

            if (channelsCreated === totalChannels) {
              console.log('All notification channels created successfully');
              resolve(true);
            }
          }
        );
      });
    });
  }
  return true; // No channel needed for iOS
};

const scheduleLocalNotification = (title, message, date = new Date(Date.now() + 5 * 1000)) => {
  if (!isNotificationSetupComplete) {
    console.log('Cannot schedule notification: Setup not complete');
    return;
  }
  try {
    PushNotification.localNotificationSchedule({
      channelId: 'classwork-channel-v3', // Match the new channelId
      title: title,
      message: message,
      date: date,
      allowWhileIdle: true,
      playSound: true,
      soundName: 'default',
      vibrate: true,
      vibration: 300,
    });
    console.log('Notification scheduled:', { title, message, date });
  } catch (error) {
    console.error('Error scheduling notification:', error);
  }
};

const setupNotifications = async () => {
  try {
    console.log('Starting notification setup...');

    // Step 1: Request permissions
    const permissionGranted = await requestNotificationPermission();

    if (permissionGranted) {
      console.log('Notification permissions granted, proceeding with setup...');

      // Step 2: Configure push notifications
      configureNotifications();
      console.log('Push notification configuration completed');

      // Step 3: Create notification channels (Android only)
      const channelCreated = await createNotificationChannel();

      if (channelCreated) {
        isNotificationSetupComplete = true;
        console.log('✅ Notifications setup completed successfully');

        // Optional: Show success message only in development
        if (__DEV__) {
          console.log('🔔 Push notifications are now enabled');
        }
      } else {
        console.error('❌ Failed to create notification channels');
        isNotificationSetupComplete = false;
      }
    } else {
      console.log('❌ Notification permissions denied by user');
      isNotificationSetupComplete = false;

      // Don't show alert here as we already showed it in requestNotificationPermission
      console.log('User can enable notifications later from device settings');
    }
  } catch (error) {
    console.error('❌ Error setting up notifications:', error);
    isNotificationSetupComplete = false;

    // Only show error alert in development or for critical errors
    if (__DEV__) {
      Alert.alert(
        'Notification Setup Error',
        `Failed to initialize notifications: ${error.message}`
      );
    }
  }

  return isNotificationSetupComplete;
};

export { setupNotifications, scheduleLocalNotification, isNotificationSetupComplete };