declare module 'react-native-push-notification' {
  export interface PushNotificationPermissions {
    alert?: boolean;
    badge?: boolean;
    sound?: boolean;
  }

  export interface PushNotificationOptions {
    onRegister?: (token: { os: string; token: string }) => void;
    onNotification?: (notification: any) => void;
    permissions?: PushNotificationPermissions;
    popInitialNotification?: boolean;
    requestPermissions?: boolean;
  }

  export interface LocalNotificationObject {
    id?: string | number;
    title?: string;
    message: string;
    playSound?: boolean;
    soundName?: string;
    number?: string;
    repeatType?: 'week' | 'day' | 'hour' | 'minute' | 'time';
    actions?: string;
    channelId?: string;
    date?: Date;
    allowWhileIdle?: boolean;
    vibrate?: boolean;
    vibration?: number;
  }

  export interface ChannelObject {
    channelId: string;
    channelName: string;
    channelDescription?: string;
    soundName?: string;
    importance?: number;
    vibrate?: boolean;
  }

  interface PushNotificationStatic {
    configure(options: PushNotificationOptions): void;
    localNotification(details: LocalNotificationObject): void;
    localNotificationSchedule(details: LocalNotificationObject): void;
    requestPermissions(permissions?: PushNotificationPermissions): Promise<PushNotificationPermissions>;
    cancelAllLocalNotifications(): void;
    cancelLocalNotifications(details: { id: string }): void;
    createChannel(channel: ChannelObject, callback?: (created: boolean) => void): void;
    channelExists(channelId: string, callback: (exists: boolean) => void): void;
    channelBlocked(channelId: string, callback: (blocked: boolean) => void): void;
    deleteChannel(channelId: string): void;
  }

  const PushNotification: PushNotificationStatic;
  export default PushNotification;
}
