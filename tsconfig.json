{"compilerOptions": {"types": ["node", "react", "react-native"], "typeRoots": ["./node_modules/@types", "./App/types"], "baseUrl": ".", "paths": {"*": ["./App/*"], "redux": ["./node_modules/redux"], "@Actions": ["./App/Actions/index"], "@Actions/*": ["./App/Actions/*"], "@Keys": ["./App/Actions/Keys/index"], "@Keys/*": ["./App/Actions/Keys/*"], "@ApiConfig": ["./App/ApiConfig/index"], "@ApiConfig/*": ["./App/ApiConfig/*"], "@AppContext": ["./App/AppContext/index"], "@AppContext/*": ["./App/AppContext/*"], "@Reducers": ["./App/Reducers/index"], "@Reducers/*": ["./App/Reducers/*"], "@Default": ["./App/Reducers/Default/index"], "@Default/*": ["./App/Reducers/Default/*"], "@Routes": ["./App/Routes/index"], "@Routes/*": ["./App/Routes/*"], "@Sagas": ["./App/Sagas/index"], "@Sagas/*": ["./App/Sagas/*"], "@Components": ["./App/Screens/Components/index"], "@Components/*": ["./App/Screens/Components/*"], "@CommonComponent": ["./App/Screens/CommonComponent/index"], "@CommonComponent/*": ["./App/Screens/CommonComponent/*"], "@SubComponents": ["./App/Screens/SubComponents/index"], "@SubComponents/*": ["./App/Screens/SubComponents/*"], "@Stores": ["./App/Stores/index"], "@Stores/*": ["./App/Stores/*"], "@Theme": ["./App/Theme/index"], "@Theme/*": ["./App/Theme/*"], "@Services": ["./App/Services/index"], "@Services/*": ["./App/Services/*"], "@Utils": ["./App/Utils/index"], "@Utils/*": ["./App/Utils/*"], "@Localization": ["./App/Localization/index"], "@Localization/*": ["./App/Localization/*"]}, "allowJs": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "isolatedModules": true, "resolveJsonModule": true, "jsx": "react-native", "lib": ["es2017"], "module": "commonjs", "moduleResolution": "node", "noEmit": true, "strict": true, "target": "esnext"}, "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"], "extends": "@react-native/typescript-config/tsconfig.json"}